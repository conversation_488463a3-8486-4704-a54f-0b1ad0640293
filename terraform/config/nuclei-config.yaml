# Headers to include with all HTTP request
header:
  - 'X-BugBounty-Hacker: github/nuclearpond'

# Directory based template execution
templates:
  - /opt # Tell <PERSON><PERSON><PERSON>i to look in the /opt directory for template files.
         # Custom templates layer unpacks .yaml files directly into /opt.

# Rate Limit configuration
rate-limit: 500
bulk-size: 50
concurrency: 50

# Lambda-specific configurations
# Set home directory to /tmp (only writable directory in Lambda)
home: /tmp
