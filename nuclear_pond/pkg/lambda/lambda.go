package lambda

import (
	"encoding/json"
	"fmt"
	"log"
	"os"

	"nuclear_pond/pkg/outputs"

	"github.com/aws/aws-sdk-go/aws"
	"github.com/aws/aws-sdk-go/aws/session"
	"github.com/aws/aws-sdk-go/service/lambda"
)

// struct for lambda invoke
type LambdaInvoke struct {
	Targets []string `json:"Targets"`
	Args    []string `json:"Args"`
	Output  string   `json:"Output"`
}

// Stage the lambda function for executing
func InvokeLambdas(payload LambdaInvoke, lambda string, output string) {
	// Bug to fix another day
	if payload.Targets[0] == "" {
		return
	}

	// convert lambdaInvoke to json string
	lambdaInvokeJson, err := json.Marshal(payload)
	if err != nil {
		log.Printf("Failed to marshal Lambda payload: %v", err)
		return
	}

	log.Printf("Invoking Lambda with %d targets", len(payload.Targets))

	// invoke lambda function
	response, err := invokeFunction(string(lambdaInvokeJson), lambda)
	if err != nil {
		log.Printf("Lambda invocation failed: %v", err)
		return
	}

	// Parse lambda response Output
	var responseInterface interface{}
	err = json.Unmarshal([]byte(response), &responseInterface)
	if err != nil {
		log.Printf("Failed to unmarshal Lambda response: %v", err)
		return
	}

	// Check if responseInterface is a map and has an "output" key
	responseMap, ok := responseInterface.(map[string]interface{})
	if !ok {
		log.Printf("Lambda response is not a valid JSON object: %v", responseInterface)
		return
	}

	// Get the output value, checking if it exists
	lambdaResponse, exists := responseMap["output"]
	if !exists || lambdaResponse == nil {
		log.Printf("Lambda response does not contain an 'output' field or it's nil")
		return
	}

	// Change outputs depending on the output
	switch output {
	case "s3":
		outputs.S3Output(lambdaResponse)
	case "cmd":
		outputs.CmdOutput(lambdaResponse)
	case "json":
		outputs.JsonOutput(lambdaResponse)
	}
}

// Execute a lambda function and return the response
func invokeFunction(payload string, functionName string) (string, error) {
	// Get region from environment variable, fallback to us-east-1 if not set
	region := os.Getenv("AWS_REGION")
	if region == "" {
		region = "us-east-1"
		log.Printf("WARNING: AWS_REGION not set, defaulting to us-east-1")
	}

	log.Printf("Invoking Lambda function: %s in region: %s", functionName, region)

	// Create a new session
	sess, sessionErr := session.NewSession(&aws.Config{
		Region: aws.String(region)},
	)
	if sessionErr != nil {
		log.Printf("Failed to create AWS session: %v", sessionErr)
		return "", sessionErr
	}

	// Create a Lambda service client.
	svc := lambda.New(sess)

	// Create the input
	input := &lambda.InvokeInput{
		FunctionName: aws.String(functionName),
		Payload:      []byte(payload),
	}

	// Invoke the lambda function
	result, err := svc.Invoke(input)
	if err != nil {
		log.Printf("Failed to invoke lambda function %s: %v", functionName, err)
		return "", err
	}

	// Check for function errors in the response
	if result.FunctionError != nil {
		log.Printf("Lambda function returned error: %s", *result.FunctionError)
		return string(result.Payload), fmt.Errorf("lambda function error: %s", *result.FunctionError)
	}

	log.Printf("Lambda invocation successful for function: %s", functionName)
	return string(result.Payload), nil
}
