package server

import (
	"encoding/base64"
	"fmt"
	"log"
	"os"
	"os/exec"
	"path/filepath"
	"strings"
	"time"

	"nuclear_pond/pkg/core"
	"nuclear_pond/pkg/helpers"

	"github.com/aws/aws-sdk-go/aws"
	"github.com/aws/aws-sdk-go/aws/session"
	"github.com/aws/aws-sdk-go/service/dynamodb"
)

func backgroundScan(scanInput Request, scanId string) {
	targets := helpers.RemoveEmpty(scanInput.Targets)
	batches := helpers.SplitSlice(targets, scanInput.Batches)
	output := scanInput.Output
	threads := scanInput.Threads
	NucleiArgs := base64.StdEncoding.EncodeToString([]byte(scanInput.Args))
	silent := true

	// Convert scanId to a valid DynamoDB key
	requestId := strings.ReplaceAll(scanId, "-", "")

	log.Println("Initiating scan with the id of ", scanId, "with", len(targets), "targets")
	storeScanState(requestId, "running")

	// Check if this is a local mode request
	localMode := scanInput.Mode == "local" ||
		os.Getenv("NUCLEARPOND_LOCAL_MODE") == "true" ||
		strings.Contains(strings.ToLower(scanInput.Args), "local") ||
		output == "local"

	if localMode {
		log.Println("Executing scan in local mode using nuclei_local_runner")
		err := executeLocalScanWithSubprocess(targets, scanInput.Args, requestId)
		if err != nil {
			log.Printf("Local scan failed: %v", err)
			storeScanState(requestId, "failed")
		} else {
			storeScanState(requestId, "completed")
		}
	} else {
		// Cloud mode - use existing Lambda execution
		functionName := os.Getenv("AWS_LAMBDA_FUNCTION_NAME")
		regionName := os.Getenv("AWS_REGION")
		dynamodbTable := os.Getenv("AWS_DYNAMODB_TABLE")
		if functionName == "" || regionName == "" || dynamodbTable == "" {
			log.Fatal("AWS environment variables not set for cloud mode")
		}

		core.ExecuteScans(batches, output, functionName, strings.Split(NucleiArgs, " "), threads, silent)
		storeScanState(requestId, "completed")
	}

	log.Println("Scan", scanId, "completed")
}

// executeLocalScanWithSubprocess runs nuclei_local_runner as a subprocess
func executeLocalScanWithSubprocess(targets []string, nucleiArgs string, requestId string) error {
	// Get the path to nuclei_local_runner executable
	nucleiRunnerPath := os.Getenv("NUCLEI_LOCAL_RUNNER_PATH")
	if nucleiRunnerPath == "" {
		// Default to looking for it in the lambda-nuclei-scanner directory
		nucleiRunnerPath = "../lambda-nuclei-scanner/nuclei_local_runner"
		if _, err := os.Stat(nucleiRunnerPath); os.IsNotExist(err) {
			// Try current directory
			nucleiRunnerPath = "./nuclei_local_runner"
			if _, err := os.Stat(nucleiRunnerPath); os.IsNotExist(err) {
				return fmt.Errorf("nuclei_local_runner executable not found. Set NUCLEI_LOCAL_RUNNER_PATH environment variable")
			}
		}
	}

	// Create temporary output file
	tempDir := os.TempDir()
	outputFile := filepath.Join(tempDir, fmt.Sprintf("nuclei_results_%s.json", requestId))
	defer os.Remove(outputFile) // Clean up after use

	// Prepare command arguments
	var cmdArgs []string

	// Add targets
	if len(targets) == 1 {
		cmdArgs = append(cmdArgs, "-targets", targets[0])
	} else {
		cmdArgs = append(cmdArgs, "-targets", strings.Join(targets, ","))
	}

	// Add nuclei arguments if provided
	if nucleiArgs != "" {
		cmdArgs = append(cmdArgs, "-args", nucleiArgs)
	}

	// Add output file
	cmdArgs = append(cmdArgs, "-output-file", outputFile)

	log.Printf("Executing nuclei_local_runner with args: %v", cmdArgs)

	// Execute the subprocess
	cmd := exec.Command(nucleiRunnerPath, cmdArgs...)

	// Capture stdout and stderr
	output, err := cmd.CombinedOutput()

	if err != nil {
		log.Printf("nuclei_local_runner execution failed: %v\nOutput: %s", err, string(output))
		return fmt.Errorf("subprocess execution failed: %v", err)
	}

	log.Printf("nuclei_local_runner completed successfully. Output: %s", string(output))

	// Read results from output file if it exists
	if _, err := os.Stat(outputFile); err == nil {
		results, readErr := os.ReadFile(outputFile)
		if readErr != nil {
			log.Printf("Warning: Could not read results file: %v", readErr)
		} else {
			log.Printf("Scan results: %s", string(results))
			// Here you could store results in DynamoDB or S3 if needed
		}
	} else {
		log.Printf("No results file generated at: %s", outputFile)
	}

	return nil
}

func storeScanState(requestId string, status string) error {
	log.Println("Stored scan state in Dynamodb", requestId, "as", status)

	sess, err := session.NewSession(&aws.Config{
		Region: aws.String(os.Getenv("AWS_REGION")),
	})
	if err != nil {
		return err
	}
	// Create DynamoDB client
	svc := dynamodb.New(sess)
	// Prepare the item to be put into the DynamoDB table
	item := &dynamodb.PutItemInput{
		TableName: aws.String(os.Getenv("AWS_DYNAMODB_TABLE")),
		Item: map[string]*dynamodb.AttributeValue{
			"scan_id": {
				S: aws.String(requestId),
			},
			"status": {
				S: aws.String(status),
			},
			"timestamp": {
				N: aws.String(fmt.Sprintf("%d", time.Now().Unix())),
			},
			"ttl": {
				N: aws.String(fmt.Sprintf("%d", time.Now().Add(time.Duration(30*time.Minute)).Unix())),
			},
		},
	}
	// Store the item in DynamoDB
	_, err = svc.PutItem(item)
	if err != nil {
		log.Println("Failed to store scan state in Dynamodb:", err)
		return err
	}

	return nil
}

// function to retrieve the scan state from DynamoDB
func getScanState(requestId string) (string, error) {
	log.Println("Retrieving scan state from Dynamodb", requestId)

	sess, err := session.NewSession(&aws.Config{
		Region: aws.String(os.Getenv("AWS_REGION")),
	})
	if err != nil {
		return "failed", err
	}
	// Create DynamoDB client
	svc := dynamodb.New(sess)
	// Prepare the item to be put into the DynamoDB table
	item := &dynamodb.GetItemInput{
		TableName: aws.String(os.Getenv("AWS_DYNAMODB_TABLE")),
		Key: map[string]*dynamodb.AttributeValue{
			"scan_id": {
				S: aws.String(requestId),
			},
		},
	}
	// Store the item in DynamoDB
	result, err := svc.GetItem(item)
	if err != nil {
		return "failed", err
	}
	return *result.Item["status"].S, nil
}
