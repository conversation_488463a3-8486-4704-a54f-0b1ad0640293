# Nuclear Pond User Guide (CLI)

This guide provides comprehensive documentation for using Nuclear Pond Command Line Interface (CLI) commands, configuration options, and operational patterns for `local` and `run` modes. For API server (`service` command) setup and usage, refer to the [API Reference](api-reference.md).

## CLI Command Overview

Nuclear Pond's CLI provides three main commands:

-   **`nuclearpond local`**: Execute Nuclei scans locally using lambda-nuclei-scanner as a subprocess.
-   **`nuclearpond run`**: Execute Nuclei scans in the cloud (currently AWS Lambda).
-   **`nuclearpond service`**: Starts the HTTP API server (detailed in the [API Reference](api-reference.md)).

This guide focuses on `local` and `run`.

## Important: New Architecture

**Nuclear Pond now acts as a pure orchestrator.** For local execution, it uses the `nuclei_local_runner` executable (built from lambda-nuclei-scanner) as a subprocess. This provides:

- Consistent behavior between local and cloud modes
- Unified API interface for both execution environments
- Simplified maintenance with a single execution engine

**Prerequisites for Local Mode:**
- The `nuclei_local_runner` executable must be built and available
- Build it using: `cd lambda-nuclei-scanner && npm run build:standalone`

## Common Concepts for CLI Operations

### Target Specification

Both `local` and `run` commands require targets to be specified in one of two ways:

-   **Single Target**: Using the `-t TARGET` flag (e.g., `-t example.com`).
-   **Target File**: Using the `-l FILE` flag (e.g., `-l targets.txt`). The file should contain one target per line.

### Nuclei Argument Encoding

All Nuclei-specific arguments (like template selection, severity, rate limits, etc.) must be **base64-encoded** before being passed to Nuclear Pond via the `-a BASE64_ARGS` flag. This ensures safe transport and execution of potentially complex arguments.

**How to Encode:**
Use the `echo -ne` command piped to `base64`. The `-n` option for `echo` is crucial as it prevents appending a newline character, which would corrupt the base64 string for Nuclei.

```bash
# Encode Nuclei arguments: '-t dns -severity high'
NUCLEI_ARGS=$(echo -ne "-t dns -severity high" | base64)
# Example output: LXQgZG5zIC1zZXZlcml0eSBoaWdo

# Use in Nuclear Pond command:
# ./nuclearpond local -t example.com -a $NUCLEI_ARGS
```

**Common Nuclei Argument Examples for Encoding:**

-   Select specific template tags: `echo -ne "-t dns,http" | base64`
-   Filter by severity: `echo -ne "-severity critical,high" | base64`
-   Set rate limit: `echo -ne "-rate-limit 100" | base64`
-   Specify custom templates path (within Nuclei's own resolution, not Nuclear Pond's `-p` flag for local mode): `echo -ne "-tp /path/to/nuclei-templates" | base64`
-   Output to a JSON file (Nuclei's output, not Nuclear Pond's): `echo -ne "-o nuclei_results.json" | base64`
-   Pass custom headers: `echo -ne "-H 'User-Agent: CustomScanner'" | base64`

**Verify Encoding:**
```bash
# echo "LXQgZG5zIC1zZXZlcml0eSBoaWdo" | base64 -d
# Expected output: -t dns -severity high
```

### General CLI Environment Variables

Some environment variables can simplify CLI usage for `run` (cloud) mode by pre-setting common AWS parameters. These are distinct from API server-specific variables.

-   `AWS_REGION`: Specifies the AWS region for cloud operations.
-   `AWS_LAMBDA_FUNCTION_NAME`: The name or ARN of the AWS Lambda function to execute for cloud scans.
-   `AWS_PROFILE`: (Optional) Specifies the AWS CLI profile to use for credentials and configuration.

Example:
```bash
export AWS_REGION=us-east-1
export AWS_LAMBDA_FUNCTION_NAME=my-nuclear-pond-lambda
# Now you can omit -r and -f flags for 'nuclearpond run'
# ./nuclearpond run -l targets.txt -a <base64_args>
```

## `nuclearpond local` - Local Execution

Execute Nuclei scans locally using lambda-nuclei-scanner as a subprocess. Nuclear Pond acts as an orchestrator, managing multiple `nuclei_local_runner` processes for parallel execution.

### Prerequisites

Before using local mode, ensure you have built the `nuclei_local_runner` executable:

```bash
cd lambda-nuclei-scanner
npm run build:standalone
# This creates the nuclei_local_runner executable
```

### Syntax
```bash
./nuclearpond local [flags]
```

### Required Parameters

-   Target: `-t TARGET` OR `-l FILE`
-   Nuclei Args: `-a BASE64_ARGS`

### Optional Parameters

| Flag             | Description                                         | Default      |
| ---------------- | --------------------------------------------------- | ------------ |
| `-c, --threads`  | Number of parallel `nuclei_local_runner` subprocess instances to run. | `4`          |
| `-b, --batch-size` | Number of targets to group per subprocess invocation.   | `1`          |
| `-s, --silent`   | Suppress Nuclear Pond's own command line output (subprocess output will still show unless also silenced via encoded args). | `false`      |

**Note**: The `-p, --templates` flag has been removed in the new architecture. Template management is now handled directly by the lambda-nuclei-scanner subprocess using Nuclei's native template resolution.

### Local Execution Output

-   **Standard Output**: By default, results from `nuclei_local_runner` subprocesses are aggregated and streamed to the terminal.
-   **Silent Mode (`-s`)**: Suppresses Nuclear Pond's orchestration output (e.g., progress messages). Subprocess output will still appear unless Nuclei itself is configured for silent operation via its base64-encoded arguments (e.g., `echo -ne "-silent" | base64`).
-   **File Output (via Nuclei)**: To save results to a file, use Nuclei's output flags within the base64-encoded arguments.
    ```bash
    # Example: Save JSON results to output.json
    ARGS_B64=$(echo -ne "-t dns -o output.json -oj" | base64)
    ./nuclearpond local -t example.com -a $ARGS_B64
    ```

### Local Execution Examples

```bash
# Basic local scan for DNS records on example.com
ARGS_B64=$(echo -ne "-t dns" | base64)
./nuclearpond local -t example.com -a $ARGS_B64 -c 2

# Scan multiple targets from targets.txt with 8 subprocess instances, batch size 5
ARGS_B64=$(echo -ne "-tags cves" | base64)
./nuclearpond local -l targets.txt -a $ARGS_B64 -c 8 -b 5

# High-performance local scanning, leveraging all processor cores for subprocess instances
# Targets batched in groups of 20, specific Nuclei templates for http, dns, ssl
CPU_CORES=$(nproc) # Linux command to get CPU cores; use `sysctl -n hw.ncpu` on macOS
ARGS_B64=$(echo -ne "-t http,dns,ssl -severity high,critical" | base64)
./nuclearpond local -l large_targets.txt -a $ARGS_B64 -c $CPU_CORES -b 20

# Using custom templates (specify template path in Nuclei args)
ARGS_B64=$(echo -ne "-tp /path/to/my/nuclei-templates -tags custom" | base64)
./nuclearpond local -l targets.txt -a $ARGS_B64 -c 4 -b 10
```

## `nuclearpond run` - Cloud Execution (AWS Lambda)

Execute Nuclei scans in a distributed manner using AWS Lambda for high scalability.

### Syntax
```bash
./nuclearpond run [flags]
```

### Required Parameters

-   Target: `-t TARGET` OR `-l FILE`
-   Nuclei Args: `-a BASE64_ARGS`
-   AWS Region: `-r REGION` (or `AWS_REGION` env var)
-   Lambda Function Name: `-f FUNCTION_NAME` (or `AWS_LAMBDA_FUNCTION_NAME` env var)

### Optional Parameters

| Flag             | Description                                              | Default |
| ---------------- | -------------------------------------------------------- | ------- |
| `-o, --output`   | Output format: `cmd` (command line), `json`, `s3` (results to S3). | `cmd`   |
| `-b, --batch-size` | Number of targets per single Lambda invocation.          | `1`     |
| `-c, --threads`  | Number of parallel Lambda invocations (concurrency).    | `1`     |
| `-s, --silent`   | Suppress Nuclear Pond's command line output.             | `false` |

### Cloud Execution Output Formats (`-o`)

-   **`cmd` (Default)**: Results from Lambda invocations are streamed to the command line. Suitable for smaller scans or quick checks.
-   **`json`**: Aggregates results from Lambda invocations into a JSON array printed to the command line. Useful for programmatic parsing by other CLI tools.
-   **`s3`**: Instructs Lambda functions to write their Nuclei output (typically JSON) to a pre-configured S3 bucket. The CLI will output the S3 object keys where results are stored. This is the recommended mode for large-scale scans to avoid overwhelming the CLI and for persistent storage. The S3 bucket and path structure are usually configured within the Lambda function's environment or deployment package.

### Cloud Execution Examples

```bash
# Set environment variables for AWS parameters
export AWS_REGION=ap-southeast-1
export AWS_LAMBDA_FUNCTION_NAME=nuclear-pond-scanner-lambda

# Basic cloud scan, output to command line
ARGS_B64=$(echo -ne "-t dns" | base64)
./nuclearpond run -t example.com -a $ARGS_B64 -o cmd

# Scan multiple targets, 8 concurrent Lambdas, batch size 10, output results as JSON to S3
ARGS_B64=$(echo -ne "-t http -severity critical -stats" | base64)
./nuclearpond run -l targets.txt -a $ARGS_B64 -o s3 -c 8 -b 10

# Silent mode, using 50 concurrent Lambdas
ARGS_B64=$(echo -ne "-t cves" | base64)
./nuclearpond run -l massive_targets.txt -a $ARGS_B64 -o s3 -c 50 -b 20 -s
```

## Performance Optimization (CLI Usage)

### Local Execution (`nuclearpond local`)

-   **Subprocess Instances (`-c`)**: Adjust based on your machine's CPU cores and memory. Too many subprocess instances can lead to resource contention and diminish returns.
    -   Start with the number of physical cores: `nuclearpond local ... -c $(nproc)` (Linux).
-   **Batch Size (`-b`)**: Larger batch sizes can reduce the overhead of starting many `nuclei_local_runner` subprocess instances but increase memory per subprocess. Find a balance based on target count and system memory.
-   **Nuclei Arguments**: Use Nuclei's own performance flags (e.g., `-rate-limit`, `-c` for Nuclei's internal concurrency) within the base64 encoded arguments for fine-grained control within each subprocess.

### Cloud Execution (`nuclearpond run`)

-   **Concurrency/Threads (`-c`)**: This controls how many Lambda functions are invoked in parallel. Monitor your AWS account's Lambda concurrency limits for the region.
    -   Start with a moderate number (e.g., 10-50) and increase based on performance and limits.
-   **Batch Size (`-b`)**: Number of targets per Lambda. 
    -   Larger batches reduce the number of Lambda invocations (cost-effective for invocation count) but increase Lambda execution time and memory. Ensure the batch can be processed within Lambda's timeout.
    -   Smaller batches lead to shorter Lambda runtimes and finer granularity but more invocations.
-   **Lambda Configuration**: Ensure your Lambda function is configured with adequate memory and timeout settings. These are set during deployment (see [Deployment Guide](../../terraform/nuclear_pond_backend/DEPLOYMENT.md)), not directly via Nuclear Pond CLI.
-   **S3 Output (`-o s3`)**: For large scans, always use S3 output to prevent data loss or CLI bottlenecks.

## Template Management (for CLI `local` and `run`)

Template management is now handled directly by the lambda-nuclei-scanner execution engine using Nuclei's native capabilities.

-   **Default Templates**: The `nuclei_local_runner` subprocess will use Nuclei's default template paths automatically.
-   **Custom Templates (via Nuclei args)**: To use custom Nuclei templates, use Nuclei's flags like `-t`, `-tags`, `-tp` (template path), `-it` (include templates), etc., within the base64-encoded `-a` arguments.
    ```bash
    # Example: Use templates from /opt/custom-nuclei-templates/
    ARGS_B64=$(echo -ne "-tp /opt/custom-nuclei-templates/ -tags internal" | base64)
    ./nuclearpond local -l internal_hosts.txt -a $ARGS_B64
    ```
    For cloud (`run`) mode, these custom templates must be packaged within your Lambda function or accessible from a shared EFS/S3 location that the Lambda can use.

-   **Template Path Resolution**: Template paths specified in Nuclei arguments are resolved by the `nuclei_local_runner` subprocess, which has the same template discovery capabilities as running Nuclei directly.

-   **Downloading/Updating Templates**: Use Nuclei's built-in commands for this, or manage your template repositories separately.
    ```bash
    # nuclei -update-templates
    ```

**Note**: The Nuclear Pond `-p` flag has been removed in the new architecture. All template management is now handled through Nuclei's native arguments passed via the `-a` parameter.

## Integration Patterns (Using CLI)

### CI/CD Integration

```bash
# Example: Bash script for CI pipeline
#!/bin/bash
set -e # Exit on error

TARGET_FILE="new_hosts.txt"
NUCLEI_SEVERITY="critical,high"
NUCLEARPOND_THREADS=8
NUCLEARPOND_BATCH_SIZE=10

# Ensure AWS env vars are set for cloud mode, or remove if local
# export AWS_REGION=...
# export AWS_LAMBDA_FUNCTION_NAME=...

NUCLEI_ARGS_B64=$(echo -ne "-severity $NUCLEI_SEVERITY -t cves,default-logins -o scan_results.json -oj" | base64)

# Using local mode as an example:
./nuclearpond local -l $TARGET_FILE -a $NUCLEI_ARGS_B64 -c $NUCLEARPOND_THREADS -b $NUCLEARPOND_BATCH_SIZE -s

# Check if scan_results.json has findings (simple check for non-empty after header/footer if JSON lines)
if [ -s scan_results.json ] && [ "$(jq length scan_results.json)" -gt 0 ]; then
  echo "Vulnerabilities found! Uploading results..."
  # aws s3 cp scan_results.json s3://my-ci-results-bucket/$(date +%Y-%m-%d)/scan_results.json
  # exit 1 # Optionally fail the build
else
  echo "No critical/high vulnerabilities found.
fi
```

### Automated Scanning (e.g., Cron Job)

```bash
# /etc/cron.d/nuclearpond_scan
# SHELL=/bin/bash
# PATH=/usr/local/sbin:/usr/local/bin:/sbin:/bin:/usr/sbin:/usr/bin:/opt/nuclearpond # Add nuclearpond path
# 0 2 * * * youruser cd /opt/nuclearpond && ./scripts/daily_scan.sh >> /var/log/nuclearpond_daily.log 2>&1

# /opt/nuclearpond/scripts/daily_scan.sh contents:
#!/bin/bash
LOG_DIR="/opt/nuclearpond/scan_logs"
RESULTS_FILE="$LOG_DIR/results-$(date +%Y-%m-%d).json"
TARGETS_FILE="/opt/nuclearpond/conf/production_targets.txt"

export AWS_REGION=us-east-1 # If using cloud mode
export AWS_LAMBDA_FUNCTION_NAME=your-prod-lambda # If using cloud mode

NUCLEI_ARGS_B64=$(echo -ne "-t cves,exposed-panels -severity high,critical -o $RESULTS_FILE -oj" | base64)

# Assuming nuclearpond binary is in /opt/nuclearpond/
/opt/nuclearpond/nuclearpond run -l $TARGETS_FILE -a $NUCLEI_ARGS_B64 -o s3 -c 20 -b 15 -s
# Or for local:
# /opt/nuclearpond/nuclearpond local -l $TARGETS_FILE -a $NUCLEI_ARGS_B64 -c 4 -b 10 -s

# Further processing/alerting based on S3 results or local $RESULTS_FILE
# For S3, you might list the bucket for new results and process them.
```

## Error Handling and Debugging (CLI)

-   **Invalid Base64 Encoding**: Double-check your `echo -ne ... | base64` command. Verify with `base64 -d`.
-   **Missing Targets/Files**: Ensure target files (`-l`) exist and are readable.
-   **Missing nuclei_local_runner**: Ensure the `nuclei_local_runner` executable is built and accessible:
    ```bash
    cd lambda-nuclei-scanner
    npm run build:standalone
    # Verify the executable exists
    ls -la nuclei_local_runner
    ```
-   **Subprocess Execution Errors**: If Nuclear Pond fails to start subprocesses, check:
    -   The `nuclei_local_runner` executable has proper permissions (`chmod +x nuclei_local_runner`)
    -   The executable is in the expected location relative to Nuclear Pond
-   **AWS Configuration Issues (for `run` mode)**:
    -   Verify AWS credentials: `aws sts get-caller-identity`.
    -   Test Lambda function access: `aws lambda get-function --function-name your-function-name`.
    -   Check CloudWatch logs for your Lambda function for runtime errors.
-   **Nuclei Errors**: If Nuclear Pond runs but the subprocess fails, the error messages from Nuclei should be visible in the output (unless `-s` is used heavily). To get more verbose output from Nuclei itself, include Nuclei's verbose flags (e.g., `-v`, `-debug`) in the base64-encoded arguments.
    ```bash
    ARGS_B64=$(echo -ne "-t dns -v" | base64)
    ./nuclearpond local -t example.com -a $ARGS_B64
    ```

For more in-depth troubleshooting related to AWS deployment, see the [Deployment Guide](../../terraform/nuclear_pond_backend/DEPLOYMENT.md). 