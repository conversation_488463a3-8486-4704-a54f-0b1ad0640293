{"name": "lambda-nuclei-scanner", "version": "1.0.0", "description": "AWS Lambda function for running Nuclei vulnerability scans", "private": true, "scripts": {"build": "GOOS=linux GOARCH=amd64 CGO_ENABLED=0 go build -o bootstrap", "build:local": "go build -o nuclei-lambda", "build:standalone": "go build -o nuclei_local_runner", "test": "go test ./...", "clean": "rm -f bootstrap nuclei-lambda nuclei_local_runner", "docker:build": "docker build -t nuclei-lambda .", "docker:run": "docker run -e BUCKET_NAME=test-bucket nuclei-lambda"}, "keywords": ["nuclei", "lambda", "security", "vulnerability-scanner", "aws"], "author": "max <<EMAIL>>", "license": "MIT"}